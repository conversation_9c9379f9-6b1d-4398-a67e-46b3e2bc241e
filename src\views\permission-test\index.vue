<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <NCard title="权限测试页面" :bordered="false" size="small" class="card-wrapper">
      <div class="flex-col gap-16px">
        <!-- 当前用户信息 -->
        <div class="p-16px bg-gray-50 rounded-8px">
          <h3 class="text-16px font-bold mb-12px">当前用户信息</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-12px">
            <div>
              <span class="font-medium">用户ID:</span>
              <span class="ml-8px">{{ authStore.userInfo.userId || '未设置' }}</span>
            </div>
            <div>
              <span class="font-medium">用户名:</span>
              <span class="ml-8px">{{ authStore.userInfo.userName || '未设置' }}</span>
            </div>
            <div>
              <span class="font-medium">角色:</span>
              <span class="ml-8px">
                <NTag v-for="role in authStore.userInfo.roles" :key="role" type="primary" size="small" class="mr-4px">
                  {{ role }}
                </NTag>
                <span v-if="!authStore.userInfo.roles.length" class="text-gray-500">无角色</span>
              </span>
            </div>
            <div>
              <span class="font-medium">按钮权限:</span>
              <span class="ml-8px">
                <NTag v-for="button in authStore.userInfo.buttons" :key="button" type="success" size="small" class="mr-4px">
                  {{ button }}
                </NTag>
                <span v-if="!authStore.userInfo.buttons.length" class="text-gray-500">无按钮权限</span>
              </span>
            </div>
          </div>
        </div>

        <!-- 权限配置信息 -->
        <div class="p-16px bg-blue-50 rounded-8px">
          <h3 class="text-16px font-bold mb-12px">权限配置信息</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-12px">
            <div>
              <span class="font-medium">权限模式:</span>
              <span class="ml-8px">{{ routeStore.authRouteMode }}</span>
            </div>
            <div>
              <span class="font-medium">超级角色:</span>
              <span class="ml-8px">{{ VITE_STATIC_SUPER_ROLE }}</span>
            </div>
            <div>
              <span class="font-medium">是否超级用户:</span>
              <span class="ml-8px">
                <NTag :type="authStore.isStaticSuper ? 'success' : 'warning'" size="small">
                  {{ authStore.isStaticSuper ? '是' : '否' }}
                </NTag>
              </span>
            </div>
            <div>
              <span class="font-medium">登录状态:</span>
              <span class="ml-8px">
                <NTag :type="authStore.isLogin ? 'success' : 'error'" size="small">
                  {{ authStore.isLogin ? '已登录' : '未登录' }}
                </NTag>
              </span>
            </div>
          </div>
        </div>

        <!-- 角色切换测试 -->
        <div class="p-16px bg-green-50 rounded-8px">
          <h3 class="text-16px font-bold mb-12px">角色切换测试</h3>
          <div class="flex gap-12px flex-wrap">
            <NButton type="primary" @click="setUserRole(['admin'])">
              设置为 Admin 角色
            </NButton>
            <NButton type="info" @click="setUserRole(['user'])">
              设置为 User 角色
            </NButton>
            <NButton type="warning" @click="setUserRole(['guest'])">
              设置为 Guest 角色
            </NButton>
            <NButton type="success" @click="setUserRole(['R_SUPER'])">
              设置为超级管理员
            </NButton>
            <NButton type="error" @click="setUserRole([])">
              清除所有角色
            </NButton>
          </div>
          <div class="mt-12px text-14px text-gray-600">
            <p>注意：角色切换后需要刷新页面才能看到菜单变化</p>
          </div>
        </div>

        <!-- 路由权限测试 -->
        <div class="p-16px bg-yellow-50 rounded-8px">
          <h3 class="text-16px font-bold mb-12px">路由权限测试</h3>
          <div class="flex gap-12px flex-wrap">
            <NButton @click="testRouteAccess('/user-management')">
              测试访问用户管理页面
            </NButton>
            <NButton @click="testRouteAccess('/home')">
              测试访问首页
            </NButton>
          </div>
        </div>

        <!-- API测试 -->
        <div class="p-16px bg-red-50 rounded-8px">
          <h3 class="text-16px font-bold mb-12px">API连接测试</h3>
          <div class="flex gap-12px flex-wrap mb-12px">
            <NButton type="primary" :loading="apiTesting" @click="testSmsApi">
              测试短信API
            </NButton>
            <NButton type="info" :loading="apiTesting" @click="testHealthCheck">
              测试健康检查
            </NButton>
            <NButton type="error" :loading="apiTesting" @click="testErrorScenario">
              测试错误场景
            </NButton>
            <NButton type="warning" @click="clearApiResults">
              清除结果
            </NButton>
          </div>
          <div class="flex gap-12px mb-12px">
            <NInput v-model:value="testPhone" placeholder="输入测试手机号" class="w-200px" />
          </div>
          <div v-if="apiResults.length" class="max-h-300px overflow-auto">
            <div v-for="(result, index) in apiResults" :key="index" class="mb-8px p-8px bg-white rounded-4px border">
              <div class="flex items-center gap-8px mb-4px">
                <NTag :type="result.success ? 'success' : 'error'" size="small">
                  {{ result.success ? '成功' : '失败' }}
                </NTag>
                <span class="text-12px text-gray-500">{{ result.timestamp }}</span>
              </div>
              <div class="text-14px">
                <div><strong>请求:</strong> {{ result.method }} {{ result.url }}</div>
                <div v-if="result.requestData"><strong>数据:</strong> {{ JSON.stringify(result.requestData) }}</div>
                <div><strong>响应:</strong> {{ result.response }}</div>
                <div v-if="result.error" class="text-red-500"><strong>错误:</strong> {{ result.error }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 菜单列表 -->
        <div class="p-16px bg-purple-50 rounded-8px">
          <h3 class="text-16px font-bold mb-12px">当前可见菜单</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8px">
            <div v-for="menu in routeStore.menus" :key="menu.key" class="p-8px bg-white rounded-4px border">
              <div class="flex items-center gap-8px">
                <component :is="menu.icon" v-if="menu.icon" class="text-16px" />
                <span class="font-medium">{{ menu.label }}</span>
              </div>
              <div class="text-12px text-gray-500 mt-4px">{{ menu.routePath }}</div>
            </div>
          </div>
        </div>
      </div>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useAuthStore } from '@/store/modules/auth';
import { useRouteStore } from '@/store/modules/route';
import { useRouterPush } from '@/hooks/common/router';
import { fetchGetSms } from '@/service/api';
import { request } from '@/service/request';

defineOptions({
  name: 'PermissionTest'
});

const authStore = useAuthStore();
const routeStore = useRouteStore();
const { routerPushByKey } = useRouterPush();

const { VITE_STATIC_SUPER_ROLE } = import.meta.env;

// API测试相关
const apiTesting = ref(false);
const testPhone = ref('13800138000');
const apiResults = ref<Array<{
  success: boolean;
  timestamp: string;
  method: string;
  url: string;
  requestData?: any;
  response: string;
  error?: string;
}>>([]);

// 设置用户角色（仅用于测试）
function setUserRole(roles: string[]) {
  // 直接修改用户信息（仅用于测试）
  authStore.userInfo.roles = roles;
  
  // 重新初始化路由
  routeStore.initAuthRoute().then(() => {
    window.$message?.success(`角色已设置为: ${roles.join(', ') || '无'}`);
    window.$message?.info('请刷新页面查看菜单变化');
  });
}

// 测试路由访问
function testRouteAccess(path: string) {
  try {
    routerPushByKey(path as any);
    window.$message?.success(`成功访问路由: ${path}`);
  } catch (error) {
    window.$message?.error(`无法访问路由: ${path}`);
    console.error('Route access error:', error);
  }
}

// 测试短信API
async function testSmsApi() {
  if (!testPhone.value.trim()) {
    window.$message?.error('请输入测试手机号');
    return;
  }

  apiTesting.value = true;
  const timestamp = new Date().toLocaleString();

  try {
    const { data, error } = await fetchGetSms(testPhone.value);

    apiResults.value.unshift({
      success: !error,
      timestamp,
      method: 'POST',
      url: '/api/v1/send-sms',
      requestData: { phone: testPhone.value },
      response: error ? `错误: ${error.message || '未知错误'}` : `成功: ${JSON.stringify(data)}`,
      error: error?.message
    });

    if (error) {
      window.$message?.error(`短信API测试失败: ${error.message}`);
    } else {
      window.$message?.success('短信API测试成功');
    }
  } catch (error: any) {
    apiResults.value.unshift({
      success: false,
      timestamp,
      method: 'POST',
      url: '/api/v1/send-sms',
      requestData: { phone: testPhone.value },
      response: `异常: ${error.message || '未知异常'}`,
      error: error.message
    });

    window.$message?.error(`短信API测试异常: ${error.message}`);
  } finally {
    apiTesting.value = false;
  }
}

// 测试健康检查
async function testHealthCheck() {
  apiTesting.value = true;
  const timestamp = new Date().toLocaleString();

  try {
    const response = await request({
      url: '/health',
      method: 'get'
    });

    apiResults.value.unshift({
      success: true,
      timestamp,
      method: 'GET',
      url: '/health',
      response: `成功: ${JSON.stringify(response)}`
    });

    window.$message?.success('健康检查成功');
  } catch (error: any) {
    apiResults.value.unshift({
      success: false,
      timestamp,
      method: 'GET',
      url: '/health',
      response: `失败: ${error.message || '未知错误'}`,
      error: error.message
    });

    // 注意：这里不需要手动显示错误消息，因为request已经会自动显示后端返回的错误消息
    console.error('健康检查失败:', error);
  } finally {
    apiTesting.value = false;
  }
}

// 测试错误场景
async function testErrorScenario() {
  apiTesting.value = true;
  const timestamp = new Date().toLocaleString();

  try {
    // 测试一个不存在的接口，应该返回404错误
    const response = await request({
      url: '/api/v1/non-existent-endpoint',
      method: 'post',
      data: { test: 'data' }
    });

    apiResults.value.unshift({
      success: true,
      timestamp,
      method: 'POST',
      url: '/api/v1/non-existent-endpoint',
      response: `意外成功: ${JSON.stringify(response)}`
    });
  } catch (error: any) {
    apiResults.value.unshift({
      success: false,
      timestamp,
      method: 'POST',
      url: '/api/v1/non-existent-endpoint',
      response: `失败: ${error.message || '未知错误'}`,
      error: error.message
    });

    console.error('错误测试:', error);
  } finally {
    apiTesting.value = false;
  }
}

// 清除API测试结果
function clearApiResults() {
  apiResults.value = [];
  window.$message?.info('已清除测试结果');
}
</script>

<style scoped>
.card-wrapper {
  @apply rd-8px shadow-sm;
}
</style>
