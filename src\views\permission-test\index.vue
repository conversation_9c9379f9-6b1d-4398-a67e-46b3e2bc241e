<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <NCard title="权限测试页面" :bordered="false" size="small" class="card-wrapper">
      <div class="flex-col gap-16px">
        <!-- 当前用户信息 -->
        <div class="p-16px bg-gray-50 rounded-8px">
          <h3 class="text-16px font-bold mb-12px">当前用户信息</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-12px">
            <div>
              <span class="font-medium">用户ID:</span>
              <span class="ml-8px">{{ authStore.userInfo.userId || '未设置' }}</span>
            </div>
            <div>
              <span class="font-medium">用户名:</span>
              <span class="ml-8px">{{ authStore.userInfo.userName || '未设置' }}</span>
            </div>
            <div>
              <span class="font-medium">角色:</span>
              <span class="ml-8px">
                <NTag v-for="role in authStore.userInfo.roles" :key="role" type="primary" size="small" class="mr-4px">
                  {{ role }}
                </NTag>
                <span v-if="!authStore.userInfo.roles.length" class="text-gray-500">无角色</span>
              </span>
            </div>
            <div>
              <span class="font-medium">按钮权限:</span>
              <span class="ml-8px">
                <NTag v-for="button in authStore.userInfo.buttons" :key="button" type="success" size="small" class="mr-4px">
                  {{ button }}
                </NTag>
                <span v-if="!authStore.userInfo.buttons.length" class="text-gray-500">无按钮权限</span>
              </span>
            </div>
          </div>
        </div>

        <!-- 权限配置信息 -->
        <div class="p-16px bg-blue-50 rounded-8px">
          <h3 class="text-16px font-bold mb-12px">权限配置信息</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-12px">
            <div>
              <span class="font-medium">权限模式:</span>
              <span class="ml-8px">{{ routeStore.authRouteMode }}</span>
            </div>
            <div>
              <span class="font-medium">超级角色:</span>
              <span class="ml-8px">{{ VITE_STATIC_SUPER_ROLE }}</span>
            </div>
            <div>
              <span class="font-medium">是否超级用户:</span>
              <span class="ml-8px">
                <NTag :type="authStore.isStaticSuper ? 'success' : 'warning'" size="small">
                  {{ authStore.isStaticSuper ? '是' : '否' }}
                </NTag>
              </span>
            </div>
            <div>
              <span class="font-medium">登录状态:</span>
              <span class="ml-8px">
                <NTag :type="authStore.isLogin ? 'success' : 'error'" size="small">
                  {{ authStore.isLogin ? '已登录' : '未登录' }}
                </NTag>
              </span>
            </div>
          </div>
        </div>

        <!-- 角色切换测试 -->
        <div class="p-16px bg-green-50 rounded-8px">
          <h3 class="text-16px font-bold mb-12px">角色切换测试</h3>
          <div class="flex gap-12px flex-wrap">
            <NButton type="primary" @click="setUserRole(['admin'])">
              设置为 Admin 角色
            </NButton>
            <NButton type="info" @click="setUserRole(['user'])">
              设置为 User 角色
            </NButton>
            <NButton type="warning" @click="setUserRole(['guest'])">
              设置为 Guest 角色
            </NButton>
            <NButton type="success" @click="setUserRole(['R_SUPER'])">
              设置为超级管理员
            </NButton>
            <NButton type="error" @click="setUserRole([])">
              清除所有角色
            </NButton>
          </div>
          <div class="mt-12px text-14px text-gray-600">
            <p>注意：角色切换后需要刷新页面才能看到菜单变化</p>
          </div>
        </div>

        <!-- 路由权限测试 -->
        <div class="p-16px bg-yellow-50 rounded-8px">
          <h3 class="text-16px font-bold mb-12px">路由权限测试</h3>
          <div class="flex gap-12px flex-wrap">
            <NButton @click="testRouteAccess('/user-management')">
              测试访问用户管理页面
            </NButton>
            <NButton @click="testRouteAccess('/home')">
              测试访问首页
            </NButton>
          </div>
        </div>

        <!-- 菜单列表 -->
        <div class="p-16px bg-purple-50 rounded-8px">
          <h3 class="text-16px font-bold mb-12px">当前可见菜单</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8px">
            <div v-for="menu in routeStore.menus" :key="menu.key" class="p-8px bg-white rounded-4px border">
              <div class="flex items-center gap-8px">
                <component :is="menu.icon" v-if="menu.icon" class="text-16px" />
                <span class="font-medium">{{ menu.label }}</span>
              </div>
              <div class="text-12px text-gray-500 mt-4px">{{ menu.routePath }}</div>
            </div>
          </div>
        </div>
      </div>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/store/modules/auth';
import { useRouteStore } from '@/store/modules/route';
import { useRouterPush } from '@/hooks/common/router';

defineOptions({
  name: 'PermissionTest'
});

const authStore = useAuthStore();
const routeStore = useRouteStore();
const { routerPushByKey } = useRouterPush();

const { VITE_STATIC_SUPER_ROLE } = import.meta.env;

// 设置用户角色（仅用于测试）
function setUserRole(roles: string[]) {
  // 直接修改用户信息（仅用于测试）
  authStore.userInfo.roles = roles;
  
  // 重新初始化路由
  routeStore.initAuthRoute().then(() => {
    window.$message?.success(`角色已设置为: ${roles.join(', ') || '无'}`);
    window.$message?.info('请刷新页面查看菜单变化');
  });
}

// 测试路由访问
function testRouteAccess(path: string) {
  try {
    routerPushByKey(path as any);
    window.$message?.success(`成功访问路由: ${path}`);
  } catch (error) {
    window.$message?.error(`无法访问路由: ${path}`);
    console.error('Route access error:', error);
  }
}
</script>

<style scoped>
.card-wrapper {
  @apply rd-8px shadow-sm;
}
</style>
