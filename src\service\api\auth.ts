import { request } from '../request';

/**
 * Login
 *
 * @param userName User name
 * @param password Password
 */
export function fetchLogin(userName: string, password: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/login',
    method: 'post',
    data: {
      userName,
      password
    }
  });
}

/**
 * @description 发送验证码
 * @param phone 手机号
 * @returns 
 */
export function fetchGetSms(phone: string) {
  return request<string>({ 
    url: '/api/v1/send-sms',
    method: 'post',
    data: {
      phone
    }
  });
}

/**
 * @description 注册
 * @param userName 
 * @param code 
 * @param password 
 * @param confirmPassword 
 * @returns 
 */
export function fetchRegister(userName: string, code: string, password: string, confirmPassword: string) {
  return request<Api.Auth.LoginToken>({
    url: '/api/v1/register',
    method: 'post',
    data: {
      userName,
      code,
      password,
      confirmPassword
    }
  });
}

/** Get user info */
export function fetchGetUserInfo() {
  return request<Api.Auth.UserInfo>({ url: '/auth/getUserInfo' });
}

/**
 * Refresh token
 *
 * @param refreshToken Refresh token
 */
export function fetchRefreshToken(refreshToken: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/refreshToken',
    method: 'post',
    data: {
      refreshToken
    }
  });
}

/**
 * return custom backend error
 *
 * @param code error code
 * @param msg error message
 */
export function fetchCustomBackendError(code: string, msg: string) {
  return request({ url: '/auth/error', params: { code, msg } });
}
