import { computed } from 'vue';
import { useCountDown, useLoading } from '@sa/hooks';
import { REG_PHONE } from '@/constants/reg';
import { $t } from '@/locales';
import { fetchSendRegisterSms } from '@/service/api';

export function useCaptcha() {
  const { loading, startLoading, endLoading } = useLoading();
  const { count, start, stop, isCounting } = useCountDown(120);

  const label = computed(() => {
    let text = $t('page.login.codeLogin.getCode');

    const countingLabel = $t('page.login.codeLogin.reGetCode', { time: count.value });

    if (loading.value) {
      text = '';
    }

    if (isCounting.value) {
      text = countingLabel;
    }

    return text;
  });

  function isPhoneValid(phone: string) {
    if (phone.trim() === '') {
      window.$message?.error?.($t('form.phone.required'));

      return false;
    }

    if (!REG_PHONE.test(phone)) {
      window.$message?.error?.($t('form.phone.invalid'));

      return false;
    }

    return true;
  }

  async function getCaptcha(phone: string) {
    const valid = isPhoneValid(phone);

    if (!valid || loading.value) {
      return;
    }

    startLoading();

    // request
    // 临时使用Mock数据，等后端准备好后可以切换回真实API
    try {
      const { data: registerSmsResponse, error } = await fetchSendRegisterSms(phone,"register");
      if (error) {
        // 如果API调用失败，使用Mock数据继续流程
        console.warn('API调用失败，使用Mock数据:', error);
        await new Promise(resolve => setTimeout(resolve, 500));
      } else {
        console.log("验证码====", registerSmsResponse.code);
        window.$message?.success?.($t('page.login.codeLogin.sendCodeSuccess'));
      }
    } catch (error) {
      // 捕获异常，使用Mock数据
      console.warn('API调用异常，使用Mock数据:', error);
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    start();

    endLoading();
  }

  return {
    label,
    start,
    stop,
    isCounting,
    loading,
    getCaptcha
  };
}
